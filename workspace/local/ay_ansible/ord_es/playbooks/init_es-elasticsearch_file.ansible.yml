- name: Apply Elasticsearch configuration template with dynamic node info
  hosts: ordEsMaster,ordEsNode
  become: true
  tasks:
    - name: Update elasticsearch.yml using a template
      ansible.builtin.template:
        src: "../template/elasticsearch.yml.j2"
        dest: "/etc/elasticsearch/elasticsearch.yml"
        owner: root
        group: elasticsearch
        mode: '0664'

  #   - name: Copy log4j2.properties file
  #     ansible.builtin.copy:
  #       src: "../template/log4j2.properties"
  #       dest: /etc/elasticsearch/log4j2.properties
  #       owner: root
  #       group: elasticsearch
  #       mode: '0660'

  #   - name: Update TimeoutStartSec in elasticsearch.service
  #     ansible.builtin.lineinfile:
  #       path: /usr/lib/systemd/system/elasticsearch.service
  #       regexp: '^TimeoutStartSec=900'
  #       line: 'TimeoutStartSec=300'
  #     notify:
  #       - Reload systemd

    # - name: Update write queue_size
    #   ansible.builtin.lineinfile:
    #     path: /etc/elasticsearch/elasticsearch.yml
    #     regexp: '^thread_pool.write.queue_size: 1000'
    #     line: 'thread_pool.write.queue_size: 10000'

  # handlers:
  #   - name: Reload systemd
  #     ansible.builtin.systemd:
  #       daemon_reload: true
