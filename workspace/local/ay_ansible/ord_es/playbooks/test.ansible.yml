- name: Configure transparent hugepages
  hosts: ordEsNode
  become: yes
  tasks:
    - name: Disable transparent hugepage enabled
      command: echo never > /sys/kernel/mm/transparent_hugepage/enabled

    - name: Disable transparent hugepage defrag
      command: echo never > /sys/kernel/mm/transparent_hugepage/defrag

    - name: Ensure commands to disable transparent hugepages are in rc.local
      blockinfile:
        path: /etc/rc.d/rc.local
        block: |
          echo never > /sys/kernel/mm/transparent_hugepage/enabled
          echo never > /sys/kernel/mm/transparent_hugepage/defrag

    - name: Ensure rc.local is executable
      file:
        path: /etc/rc.d/rc.local
        mode: '0755'
        state: file