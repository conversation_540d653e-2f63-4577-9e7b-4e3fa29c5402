[ordEsNode]
172.17.0.48 ansible_ssh_user=root ansible_ssh_password=h<PERSON><PERSON><PERSON><PERSON><PERSON>@48 node_name="ord-es-node-14-48" node_roles="[data, ingest]"
172.17.0.40 ansible_ssh_user=root ansible_ssh_password=h<PERSON><PERSON><PERSON><PERSON><PERSON>@40 node_name="ord-es-node-15-40" node_roles="[data, ingest]"
172.17.0.51 ansible_ssh_user=root ansible_ssh_password=hMs<PERSON><PERSON><PERSON><PERSON>@51 node_name="ord-es-node-12-51" node_roles="[data, ingest]"
172.17.0.49 ansible_ssh_user=root ansible_ssh_password=h<PERSON><PERSON><PERSON><PERSON><PERSON>@49 node_name="ord-es-node-10-49" node_roles="[data, ingest]"
172.17.0.34 ansible_ssh_user=root ansible_ssh_password=hMs<PERSON><PERSON><PERSON><PERSON>@34 node_name="ord-es-node-11-34" node_roles="[data, ingest]"
172.17.0.44 ansible_ssh_user=root ansible_ssh_password=h<PERSON><PERSON><PERSON><PERSON><PERSON>@44 node_name="ord-es-node-13-44" node_roles="[data, ingest]"
172.17.0.39 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@39 node_name="ord-es-node-06-39" node_roles="[data, ingest]"
172.17.0.50 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@50 node_name="ord-es-node-09-50" node_roles="[data, ingest]"
172.17.0.43 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@43 node_name="ord-es-node-05-43" node_roles="[data, ingest]"
172.17.0.58 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@58 node_name="ord-es-node-07-58" node_roles="[data, ingest]"
172.17.0.18 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@18 node_name="ord-es-node-08-18" node_roles="[data, ingest]"
172.17.0.46 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@46 node_name="ord-es-node-02-46" node_roles="[data, ingest]"
172.17.0.30 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@30 node_name="ord-es-node-04-30" node_roles="[data, ingest]"
172.17.0.35 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@35 node_name="ord-es-node-03-35" node_roles="[data, ingest]"
172.17.0.32 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@32 node_name="ord-es-node-01-32" node_roles="[data, ingest]"

; 172.19.0.244 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@244 node_name="ord-es-node-16-244" node_roles="[data, ingest]"
; 172.19.0.235 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@235 node_name="ord-es-node-17-235" node_roles="[data, ingest]"
; 172.19.0.237 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@237 node_name="ord-es-node-18-237" node_roles="[data, ingest]"
; 172.19.0.238 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@238 node_name="ord-es-node-19-238" node_roles="[data, ingest]"
; 172.19.0.236 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@236 node_name="ord-es-node-20-236" node_roles="[data, ingest]"

[ordEsMaster]
; 172.17.0.53 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@53 node_name="ord-es-master-01-53" node_roles="[master]"
; 172.17.0.54 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@54 node_name="ord-es-master-02-54" node_roles="[master]"
; 172.17.0.55 ansible_ssh_user=root ansible_ssh_password=hMsJjJY@55 node_name="ord-es-master-03-55" node_roles="[master]"

[ordEsKibana]
; 172.17.0.16 ansible_ssh_user=root ansible_ssh_password=dBrCxBy@1111 node_name="ord-es-kibana-16"

[ordEsTest]
; 172.19.0.238 ansible_ssh_user=root ansible_ssh_password=dBrCxBy@1111 node_name="ord-es-test"
