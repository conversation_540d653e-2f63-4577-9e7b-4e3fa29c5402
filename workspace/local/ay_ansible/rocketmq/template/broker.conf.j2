# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

brokerClusterName = DefaultCluster
namesrvAddr=***********:9876;***********:9876
brokerName={{ broker_name }}
brokerId={{ broker_id }}
deleteWhen = 04
fileReservedTime = 24
brokerRole = {{ broker_role }}
flushDiskType = ASYNC_FLUSH
accessMessageInMemoryMaxRatio = 5
diskMaxUsedSpaceRatio=75
slaveReadEnable = true
autoCreateTopicEnable = false
autoCreateSubscriptionGroup = false
sendMessageThreadPoolNums=64
transientStorePoolEnable=true
transferMsgByHeap=false
useReentrantLockWhenPutMessage=true
waitTimeMillsInSendQueue=400
messageDelayLevel=1s 5s 15s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 30m 1h 2h 4h


storePathRootDir=/data/srv/rocketmq-4.5.0/store
storePathConsumerQueue=/data/srv/rocketmq-4.5.0/store/consumequeue
storePathCommitLog=/data/srv/rocketmq-4.5.0/store/commitlog
storePathIndex=/data/srv/rocketmq-4.5.0/store/index
storeCheckpoint=/data/srv/rocketmq-4.5.0/store/checkpoint
abortFile=/data/srv/rocketmq-4.5.0/store/abort