[master]
172.19.0.11 ansible_ssh_user=root ansible_ssh_password=dBrCxBy@1111 broker_name="broker-5" broker_id="0" broker_role="ASYNC_MASTER" config_suffix="" service_name="master" exportor_group="rocketmq-new"  exportor_name="rocketmq-5"
172.19.0.19 ansible_ssh_user=root ansible_ssh_password=dBrCxBy@1111 broker_name="broker-6" broker_id="0" broker_role="ASYNC_MASTER" config_suffix="" service_name="master" exportor_group="rocketmq-new"  exportor_name="rocketmq-6"

; [slave]
; 172.19.0.12 ansible_ssh_user=root ansible_ssh_password=dBrCxBy@1111 broker_name="broker-5" broker_id="1" broker_role="SLAVE" config_suffix="-slave" service_name="slave" exportor_group="rocketmq-new"  exportor_name="rocketmq-5-salve"
; 172.19.0.20 ansible_ssh_user=root ansible_ssh_password=dBrCxBy@1111 broker_name="broker-6" broker_id="1" broker_role="SLAVE" config_suffix="-slave" service_name="slave" exportor_group="rocketmq-new"  exportor_name="rocketmq-6-salve"