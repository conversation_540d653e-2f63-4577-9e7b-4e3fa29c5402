- name: MongoD profiling
  hosts: mg-d
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"
  vars_files:
    - ../vars/mongo_vars.yml

  tasks:
  - name: 设置 trade 数据库的性能分析级别
    shell: |
      mongo -u root -p {{ mongo_password }} --authenticationDatabase admin trade --eval "db.setProfilingLevel(1, { slowms:100, sampleRate: 0.5 });"
    register: trade_profiling_result

  - name: 显示 trade 数据库性能分析设置结果
    debug:
      var: trade_profiling_result.stdout

  - name: 设置 item 数据库的性能分析级别
    shell: |
      mongo -u root -p {{ mongo_password }} --authenticationDatabase admin item --eval "db.setProfilingLevel(1, { slowms:100, sampleRate:0.5 })"
    register: item_profiling_result

  - name: 显示 item 数据库性能分析设置结果
    debug:
      var: item_profiling_result.stdout

- name: MongoS profiling
  hosts: mg-s
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"
  vars_files:
    - ../vars/mongo_vars.yml

  tasks:
  - name: 设置 mongos 数据库的性能分析级别
    shell: |
      mongo -u root -p {{ mongo_password }} --authenticationDatabase admin admin --eval "db.setProfilingLevel(0, { slowms:100, sampleRate: 0.5 });"
    register: mongos_profiling_result

  - name: 显示 mongos 数据库性能分析设置结果
    debug:
      var: mongos_profiling_result.stdout