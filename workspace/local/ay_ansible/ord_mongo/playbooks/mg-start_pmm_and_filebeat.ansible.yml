- name: Update Mongod configuration
  hosts: mg-d
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"

  tasks:
    - name: Add Mongod PMM
      shell: >
        pmm-admin remove mongodb d-{{ inventory_hostname }}:27017 && pmm-admin config --server-url *********************************** --server-insecure-tls --force && pmm-admin add mongodb --cluster=trade --replication-set={{ shard }} --username=root --password=Zx891Ajf d-{{ inventory_hostname }}:27017 127.0.0.1:27017
    
    - name: start Mongod filebeat
      shell: |
        cd ~/source/filebeat/filebeat
        nohup ./filebeat -e > /dev/null 2>&1 &
        ps aux|grep filebeat


- name: Update Mongos configuration
  hosts: mg-s
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"

  tasks:
    - name: Add Mongos PMM
      shell: >
        pmm-admin config --server-url *********************************** --server-insecure-tls --force

    - name: start Mongos filebeat
      shell: |
        cd ~/source/filebeat/filebeat
        nohup ./filebeat -e > /dev/null 2>&1 &
        ps aux|grep filebeat