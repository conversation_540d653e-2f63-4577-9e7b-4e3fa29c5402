- name: MongoDB shell
  hosts: mg-s
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"
  vars_files:
    - ../vars/mongo_vars.yml

  tasks:
- name: Add StartLimit parameters to mongod.service
  lineinfile:
  path: /usr/lib/systemd/system/mongod.service
  insertafter: '^[Service]'
  line: "{{ item }}"
  with_items:
  - 'StartLimitInterval=300s'
  - 'StartLimitBurst=3'
  register: mongod_service_update_result

- name: Reload systemd daemon
  systemd:
  daemon_reload: yes

- name: 显示 mongod.service 更新结果
  debug:
    var: mongod_service_update_result
