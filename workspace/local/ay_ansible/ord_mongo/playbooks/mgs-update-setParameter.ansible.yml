- name: MongoDB shell
  hosts: mg-s
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"

  tasks:
  - name: 设置 ShardingTaskExecutorPoolMinSize
    shell: |
      mongo -u root -p {{ mongo_password }} --authenticationDatabase admin admin --eval "db.adminCommand('setParameter', {'ShardingTaskExecutorPoolMinSize': 15})"
    register: sharding_task_executor_pool_min_size_result

  - name: 显示 ShardingTaskExecutorPoolMinSize
    debug:
      var: sharding_task_executor_pool_min_size_result.stdout

  - name: 在 /etc/mongod.conf 文件末尾追加 setParameter 配置
    blockinfile:
      path: /etc/mongod.conf
      block: |
        setParameter:
          ShardingTaskExecutorPoolMinSize: 15
      marker: "# {mark} ANSIBLE MANAGED BLOCK - setParameter"
      # state: absent
    register: mongod_conf_update_result

  - name: 显示 mongod.conf 更新结果
    debug:
      var: mongod_conf_update_result
