- name: Add user 'aiyong' with password and sudo privileges
  hosts: all
  become: true
  tasks:
    - name: Generate password
      ansible.builtin.set_fact:
        aiyong_password: "JjWtYyH{{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'].split('.')[-1] | string }}"
        root_password: "hMsJjJY{{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'].split('.')[-1] | string }}"
    - name: Ensure group exists
      ansible.builtin.group:
        name: "aiyong"
        state: present

    - name: Add aiyong to the system
      ansible.builtin.user:
        name: "aiyong"
        group: "aiyong"
        password: "{{ aiyong_password | password_hash('sha512') }}"
        update_password: always
        state: present
        createhome: true
        shell: /usr/bin/zsh

    - name: Add aiyong to sudoers
      ansible.builtin.lineinfile:
        path: /etc/sudoers
        state: present
        regexp: '^aiyong'
        line: 'aiyong ALL=(ALL:ALL) NOPASSWD:ALL'
        validate: 'visudo -cf %s'

    - name: Change root password
      ansible.builtin.user:
        name: "root"
        password: "{{ root_password | password_hash('sha512') }}"
        update_password: always
        state: present
        shell: /usr/bin/zsh

    - name: Print password
      ansible.builtin.debug:
        msg:
          - "The aiyong password for aiyong is: {{ aiyong_password }}"
          - "The root password for aiyong is: {{ root_password }}"
