- name: Configure Elasticsearch filesystem
  hosts: ordEsNode
  become: true
  tasks:
    - name: Format the disk with XFS filesystem
      ansible.builtin.command: mkfs.xfs -f /dev/vdb -b size=4096

    - name: Mount the filesystem
      ansible.posix.mount:
        path: /var/lib/elasticsearch
        src: /dev/vdb
        fstype: xfs
        opts: defaults,noatime,nodiratime
        state: mounted

    - name: Add entry to /etc/fstab
      ansible.posix.mount:
        path: /var/lib/elasticsearch
        src: /dev/vdb
        fstype: xfs
        opts: defaults,noatime,nodiratime
        state: present

    - name: Change ownership of /path/to/directory
      ansible.builtin.file:
        path: /var/lib/elasticsearch
        state: directory
        owner: elasticsearch
        group: elasticsearch
        mode: '2770'
        
    - name: Update elasticsearch.yml using a template
      ansible.builtin.template:
        src: "../template/elasticsearch.yml.j2"
        dest: "/etc/elasticsearch/elasticsearch.yml"
        owner: root
        group: elasticsearch
        mode: '0664'