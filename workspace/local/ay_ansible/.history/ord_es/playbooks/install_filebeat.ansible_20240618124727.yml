- name: Install Filebeat
  hosts: ordEsNode
  become: true

  tasks:
    - name: Copy Filebeat rpm to managed hosts
      ansible.builtin.copy:
        src: "../template/filebeat-7.17.22-x86_64.rpm"
        dest: /root/source/filebeat-7.17.22-x86_64.rpm
        mode: '0660'

    - name: Install Filebeat
      ansible.builtin.yum:
        name: /root/source/filebeat-7.17.22-x86_64.rpm
        state: present

    - name: Enable Elasticsearch Filebeat module
      ansible.builtin.command: filebeat modules enable elasticsearch

    - name: Copy filebeate.yml file
      ansible.builtin.copy:
        src: "../template/filebeat.yml"
        dest: /etc/filebeat/filebeat.yml
        owner: root
        group: root
        mode: '0600'

    - name: Copy filebeate-elastcsearch.yml file
      ansible.builtin.copy:
        src: "../template/filebeat-elasticsearch.yml"
        dest: /etc/filebeat/modules.d/elasticsearch.yml
        owner: root
        group: root
        mode: '0644'

    - name: Ensure the filebeat service is started
      ansible.builtin.service:
        name: filebeat
        # state: stopped
        state: started
        enabled: true
