- name: Install node_exporter
  hosts: ordEsKibana
  become: true

  tasks:
    - name: Copy node_exporter binary to managed hosts
      ansible.builtin.copy:
        src: /Users/<USER>/workspace/local/ay_ansible/ord_es/template/node_exporter-1.8.1.linux-amd64.tar.gz
        dest: /root/source/node_exporter.tar.gz
        mode: '0660'

    - name: Extract node_exporter binary
      ansible.builtin.unarchive:
        src: /root/source/node_exporter.tar.gz
        remote_src: true
        dest: /root/source
        mode: "0755"

    - name: Move node_exporter binary to /usr/local/bin
      ansible.builtin.copy:
        src: /tmp/node_exporter-1.8.1.linux-amd64/node_exporter
        dest: /usr/local/bin/node_exporter
        mode: "0755"
        remote_src: true
