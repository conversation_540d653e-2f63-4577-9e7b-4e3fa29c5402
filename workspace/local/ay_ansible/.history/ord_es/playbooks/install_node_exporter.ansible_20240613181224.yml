- name: Install node_exporter
  hosts: ord<PERSON><PERSON><PERSON><PERSON>
  become: true

  tasks:
    - name: Copy node_exporter binary to managed hosts
      ansible.builtin.copy:
        src: ./template/node_exporter-1.8.1.linux-amd64.tar.gz
        dest: /root/source/node_exporter-1.8.1.linux-amd64.tar.gz
        mode: '0660'

    - name: Create node_exporter directory
      ansible.builtin.file:
        path: /root/source/node_exporter/
        state: directory
        mode: '0660'

    - name: Extract node_exporter binary
      ansible.builtin.unarchive:
        src: /root/source/node_exporter-1.8.1.linux-amd64.tar.gz
        remote_src: true
        dest: /root/source/node_exporter/
        mode: "0755"

    - name: Move node_exporter binary
      ansible.builtin.copy:
        src: /root/source/node_exporter/node_exporter-1.8.1.linux-amd64/node_exporter
        dest: /root/source/node_exporter/node_exporter
        mode: "0755"
        remote_src: true

    - name: Delete extracted node_exporter directory
      ansible.builtin.file:
        path: /root/source/node_exporter/node_exporter-1.8.1.linux-amd64
        state: absent

    - name: Create systemd service
      ansible.builtin.copy:
        src: ./template/node_exporter.service
        dest: /etc/systemd/system/node_exporter.service
        mode: '0644'
