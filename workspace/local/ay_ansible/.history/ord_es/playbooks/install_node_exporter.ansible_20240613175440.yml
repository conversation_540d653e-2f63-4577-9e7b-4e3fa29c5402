- name: Install node_exporter
  hosts: node_exporter
  become: true

  tasks:
    - name: Copy node_exporter binary to managed hosts
      copy:
        src: /Users/<USER>/workspace/local/ay_ansible/ord_es/template/node_exporter-1.8.1.linux-amd64.tar.gz
        dest: /root/source/node_exporter.tar.gz

    - name: Extract node_exporter binary  
      unarchive:
        src: /root/source/node_exporter.tar.gz
        remote_src: yes
        dest: /root/source
        mode: 0755
        
    - name: Move node_exporter binary to /usr/local/bin
      copy:
        src: /tmp/node_exporter-1.8.1.linux-amd64/node_exporter
        dest: /usr/local/bin/node_exporter
        mode: 0755
        remote_src: yes