- name: Update user configuration
  hosts: mg-s
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"

  tasks:
    # - name: Set hostname
    #   hostname:
    #     name: "{{ node_name }}"

    # - name: Add MongoDB monitoring
    #   shell: >
    #     pmm-admin config --server-url *********************************** --server-insecure-tls --force

    - name: start filebeat
      shell: |
        cd ~/source/filebeat/filebeat
        nohup ./filebeat -e > /dev/null 2>&1 &
        ps aux|grep filebeat


