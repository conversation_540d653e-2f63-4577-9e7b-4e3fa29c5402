- name: Update user configuration
  hosts: mg-d
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"

  tasks:
    - name: Set hostname
      hostname:
        name: "{{ node_name }}"

    - name: Add MongoDB monitoring
      shell: >
        pmm-admin remove mongodb d-{{ inventory_hostname }}:27017 && pmm-admin config --server-url https://admin:Zx891Ajf@************ --server-insecure-tls --force && pmm-admin add mongodb --cluster=trade --replication-set={{ shard }} --username=root --password=Zx891Ajf d-{{ inventory_hostname }}:27017 127.0.0.1:27017
