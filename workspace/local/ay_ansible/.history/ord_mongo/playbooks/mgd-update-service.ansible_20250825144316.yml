- name: MongoDB shell
  hosts: mg-d
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"

  tasks:
    - name: Add StartLimit parameters to mongod.service
      blockinfile:
        path: /usr/lib/systemd/system/mongod.service
        insertafter: '^[Service]'
        block: |
          StartLimitInterval=300s
          StartLimitBurst=3
        marker: "# {mark} ANSIBLE MANAGED BLOCK - StartLimit parameters"
      register: mongod_service_update_result

    - name: Reload systemd daemon
      ansible.builtin.systemd:
        daemon_reload: true

    - name: 显示 mongod.service 更新结果
      debug:
        var: mongod_service_update_result
