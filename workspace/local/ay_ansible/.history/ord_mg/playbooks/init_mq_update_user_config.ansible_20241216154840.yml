- name: Update user configuration
  hosts: all
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ ansible_ssh_password }}"

  tasks:
    - name: 修改 aiyong 用户密码
      ansible.builtin.user:
        name: aiyong
        password: "{{ new_password | password_hash('sha512') }}"
        update_password: always
        state: present

    - name: 确保文件中 PermitRootLogin 设置为 yes
      ansible.builtin.lineinfile:
        path: /etc/ssh/sshd_config
        regexp: '^#?PermitRootLogin'
        line: 'PermitRootLogin yes'
        state: present
        backup: false

    - name: 重启 SSH 服务
      ansible.builtin.systemd:
        name: sshd
        state: restarted
