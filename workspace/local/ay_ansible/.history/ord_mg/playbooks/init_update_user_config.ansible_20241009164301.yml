- name: Update user configuration
  hosts: all
  become: true
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ your_sudo_password }}"
    new_password: "new_aiyong_password"

  tasks:
    - name: Generate password
      ansible.builtin.set_fact:
        aiyong_password: "JjWtYyH{{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'].split('.')[-1] | string }}"
        root_password: "hMsJjJY{{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'].split('.')[-1] | string }}"
        
    - name: 修改 aiyong 用户密码
      ansible.builtin.user:
        name: aiyong
        password: "{{ new_password | password_hash('sha512') }}"

    - name: 确保文件中 PermitRootLogin 设置为 yes
      ansible.builtin.lineinfile:
        path: /etc/ssh/sshd_config
        regexp: '^#?PermitRootLogin'
        line: 'PermitRootLogin yes'
        state: present
        backup: false

    - name: 重启 SSH 服务
      ansible.builtin.systemd:
        name: sshd
        state: restarted
