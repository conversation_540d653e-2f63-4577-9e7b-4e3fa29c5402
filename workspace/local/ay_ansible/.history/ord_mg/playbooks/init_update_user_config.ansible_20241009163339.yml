- hosts: all
  become: yes
  vars:
    ansible_user: aiyong
    ansible_become_pass: "{{ your_sudo_password }}"
    new_password: "new_aiyong_password"
    
  tasks:
    - name: 修改 aiyong 用户密码
      user:
        name: aiyong
        password: "{{ new_password | password_hash('sha512') }}"
    
    - name: 确保文件中 PermitRootLogin 设置为 yes
      lineinfile:
        path: /etc/ssh/sshd_config
        regexp: '^#?PermitRootLogin'
        line: 'PermitRootLogin yes'
        state: present
        backup: yes
      
    - name: 重启 SSH 服务
      systemd:
        name: sshd
        state: restarted