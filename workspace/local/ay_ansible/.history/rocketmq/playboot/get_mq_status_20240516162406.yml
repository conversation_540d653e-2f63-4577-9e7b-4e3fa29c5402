- name: 获取Elasticsearch服务状态
  hosts: ordEsMaster,ordEsNode
  become: true
  tasks:
    - name: 收集服务状态信息
      ansible.builtin.service_facts:

    - name: 打印Elasticsearch服务状态
      ansible.builtin.debug:
        msg: "Elasticsearch service is {{ ansible_facts.services['elasticsearch.service'].state }}"
      when: "'elasticsearch.service' in ansible_facts.services"
      
    - name: 打印Elasticsearch服务状态
      ansible.builtin.debug:
        msg: "Elasticsearch service is {{ ansible_facts.services['elasticsearch.service'].state }}"
      when: "'elasticsearch.service' in ansible_facts.services"