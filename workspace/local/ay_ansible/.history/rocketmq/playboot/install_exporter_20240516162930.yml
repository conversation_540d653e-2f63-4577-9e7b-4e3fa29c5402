- name: Install node_exporter
  hosts: all
  become: true
  tasks:
    - name: Create node_exporter dir
      ansible.builtin.shell: mkdir -p /data/srv/node_exporter;
    - name: Download node_exporter
      ansible.builtin.copy: src=./material/node_exporter dest=/data/srv/node_exporter/
    - name: Create node_exporter config
      ansible.builtin.shell: echo outside_node_exporter{name=\"{{ name }}\", group=\"{{ group }}\"} 1 > /data/srv/node_exporter/config.prom
      args:
        chdir: /data/srv/node_exporter/
    - name: kill old node_exporter
      ansible.builtin.shell: ps -fe | grep "/data/srv/node_exporter/node_exporter" | grep -v grep | tr -s " "|cut -d" " -f2 | xargs kill -9
      ignore_errors: yes
    - name: start node_exporter
      ansible.builtin.shell: chmod a+x /data/srv/node_exporter/node_exporter ; nohup /data/srv/node_exporter/node_exporter --collector.textfile.directory=/data/srv/node_exporter/ >> /data/srv/node_exporter/out.log 2>&1 & sleep 2
      args:
        chdir: /data/srv/node_exporter/
