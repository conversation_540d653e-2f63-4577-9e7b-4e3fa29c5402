- name: Configure Elasticsearch filesystem
  hosts: ordEsNode
  become: true
  tasks:
    - name: Format the disk with XFS filesystem
      ansible.builtin.command: mkfs.xfs -f /dev/vdb -b size=4096

    - name: Mount the filesystem
      ansible.posix.mount:
        path: /data/srv/rocketmq-4.5.0/store
        src: /dev/vdb
        fstype: xfs
        opts: defaults,noatime,nodiratime
        state: mounted

    - name: Add entry to /etc/fstab
      ansible.posix.mount:
        path: /data/srv/rocketmq-4.5.0/store
        src: /dev/vdb
        fstype: xfs
        opts: defaults,noatime,nodiratime
        state: present

    - name: Change ownership of /path/to/directory
      ansible.builtin.file:
        path: /data/srv/rocketmq-4.5.0/store
        state: directory
        owner: aiyong
        group: aiyong
        mode: '755'
