尊敬的开发者：

您好！

2024年618大促即将到来（**抢先购正式售卖：5月20日20点；活动正式售卖时间：5月31日20点**），为了保障大促期间商家系统稳定运行，请您积极配合平台完成大促保障工作。**今年商家经营工具（ERP、订单管理、CRM等）将于2024年5月20日10点到5月21日10点、5月31日10点到6月1日10点期间（以下简称”保障期”）进行封网，淘宝购物小程序应用将于5月20日10点到5月21日10点、5月31日10点到6月1日10点期间进行封网**，开发者需在封网之前完成所有大促保障事项，包含但不限于本公告列出内容，请各位开发者结合实际情况进行保障。

保障事项及保障内容：

| 保障事项

 | 保障内容

 | 起止时间

 |
| --- | --- | --- |
| 大促退款不降级

 | 618大促未发货订单的申请退款功能不关闭，消费者可随时发起退款申请。为了确保高峰数据处理的准确性，服务商需要重点关注并评估此变化点。并完成对退款保障方案的对接。详细保障方案参见：https://open.taobao.com/doc.htm?docId=120994&docType=1&spm=0.0.0.0.XCW6bz

 | 5月6日10点～5月19日24点
 |
| 商家经营工具压测

 | 1.开放平台会根据业务优先级下发重点商家压测任务，开发者需要按照压测任务要求通过生产环境压测，压测结果以任务中心为主。2.授权商家超过1000家（含）以上的ERP类开发者或授权商家超过10000家（含）以上的订单管理类开发者的应用需要按照2023年618大促当天2倍单量通过生产环境压测，压测结果以任务中心为主。3.拥有物流发货接口权限且授权规模超过50家店铺的应用，需要按照2023年618大促当天2倍单量通过日常或生产环境压测，压测结果以任务中心为主。4.开放平台会根据业务要求下发改地址、隐私保护全链路、退款等压测任务，开发者需要按照压测任务要求通过生产环境压测，压测结果以任务中心为主。附：详情参考https://open.taobao.com/doc.htm?docId=103188&docType=1、https://open.taobao.com/doc.htm?docId=118081&docType=1&spm=0.0.0.0.FNUeDR
 | 5月6日10点～5月17日0点

 |
| 小程序应用压测

 | 1.购物小程序日常DAU峰值达到50000，平台针对C端应用，按照日常访问量的5倍下发压测任务，B端应用按照日常访问量的2倍下发压测任务，开发者需要按照压测任务要求通过生产环境压测，压测结果以任务中心为主。

 |  |
| 系统调优

 | 1.针对应用系统性能瓶颈需要在封网之前完成各项优化，详情参考压测报告优化建议。2.应用部署架构应采用高可用分布式架构，具备较强的水平扩展能力、稳定性及安全防护能力，消除单点部署、实现异常降级，请参考https://open.taobao.com/doc.htm?spm=a219a.7386653.1.52.5a5a5c43uo3K0L&docId=109298&docType=1。
 |  |
| 应用监控

 | 1.平台确定的合作服务商中须具备订单全链路监控能力，且须回传订单作业处理状态，包含但不限于订单转单、通知配货、库存查询、仓库作业节点等信息。请确保您的应用在大促期间能够及时有效地回传商家订单作业处理状态，不得擅自关闭或降级，若出现特殊情况导致无法正常回传的，需要提前与平台报备。

 |  |
| 云资源优化

 | 1.针对平台诊断出的高使用率云资源（如出现CPU使用率过高、磁盘占用率过高、带宽使用率过高等情况），在平台诊断报告中给出的优化截止时间前完成优化或弹性升级。
2.针对平台诊断出需要优化的云资源，在平台诊断报告中给出的优化截止时间前，按平台要求进行优化。包括但不限于：对数据库存储的订单、会员等数据超过1年及以上配备历史数据库，并实施冷热数据分离；对数据库单表数据量超过500万条或单表文件大小(行长*行数)超过10G的进行分库分表。
3.对平台诊断出的低规格云资源（如CPU 1核、内存 2G等），在平台诊断报告中给出的优化截止时间前完成优化或弹性升级。
4.对平台确认存在业务性能风险的数据库，在平台诊断报告中给出的优化截止时间前，完成业务库与推送库分离。

 |  |
| 应用走查

 | 开发者需要在封网前对应用系统进行全面自检，包含但不限于：
1.APPKEY调用量扩容检查
2.商家授权到期
3.云资源到期检查
4.域名备案检查
5.云资源高水位实例检查
6.RDS慢SQL优化
7.网络&网关检查
8.云资源账户余额和API账户余额检查

 |  |
| 应急预案与演练

 | 开发者应用制定自身系统的紧急预案：包含但不限于缓存策略、降级&限流方案、灾备策略等。应急预案在大促封网前完成演练，尤其高并发业务务必演练。

 |  |
| 商家经营工具云资源扩容

 | 1.根据压测、日常和大促云资源使用率等指标未达标的服务器、数据库、带宽等资源进行扩容升级。

 | 抢先购：5月19日10点之前
正式售卖：5月30日10点之前
 |
| 淘宝购物小程序云资源扩容

 | 1.根据压测、日常和大促云资源使用率等指标未达标的服务器、数据库、带宽等资源进行扩容升级。

 | 抢先购：5月19日10点之前
正式售卖：5月30日10点之前

 |
| 商家经营工具封版管控
 | 1.平台禁止开发者在封网期间擅自实施应用修改、更新与发布、云资源弹性升级等可能影响应用稳定性的操作，详见https://open.taobao.com/doc.htm?spm=a219a.7386653.1.45.f37e5c43nk83aK&docId=118104&docType=1。2.封网期如需紧急发布或云资源弹性升级，请通过https://console.cloud.tmall.com/home#/>https://console.cloud.tmall.com/component/changefreeCenter#/，提交应用紧急发布申请。
 | 5月20日10点～6月1日10点
 |
| 淘宝购物小程序封版管控
 | 1.平台禁止开发者在封网期间擅自实施应用修改、更新与发布、云资源弹性升级等可能影响应用稳定性的操作，详见https://open.taobao.com/doc.htm?spm=a219a.7386653.1.45.f37e5c43nk83aK&docId=118104&docType=1。2.封网期如需紧急发布或云资源弹性升级，请通过https://console.cloud.tmall.com/home#/>https://console.cloud.tmall.com/component/changefreeCenter#/，提交应用紧急发布申请。
 | 5月20日10点～6月1日10点
 |
| 应用安全

 | 1.参考https://open.taobao.com/doc.htm?spm=a219a.7386653.0.0.5712669aUsWouI&docId=109298&docType=1中的应用安全基础规范，确保主机和应用的安全防御和检测启用，防止大促期间因攻击导致的系统不稳定。
 | 5月6日～6月21日
 |

同时，为了提升开发者对618保障工作的重视程度，针对在2024年618保障工作中出现问题的开发者收回平台之前给予的部分或全部权益。

问题及处理措施：

| 出现的问题

 | 处理措施

 |
| --- | --- |
| 1.未按照平台要求实施和完成大促保障工作，包括重点商家压测、重点业务保障、应用压测、订单全链路监控实施、应用和云资源优化、云资源扩容、应用发布封版等内容。

 | 1.收回给予开发者的聚石塔云资源折扣优惠，开发者购买聚石塔云资源恢复原价。2.收回给予开发者的开放平台技术服务费折扣优惠。3.如服务商应用在618期间出现稳定性问题导致影响商家业务，开放平台将会按照https://open.taobao.com/doc.htm?spm=a219a.15212433.0.0.4ec9669aBvrD8m&docId=108273&docType=1进行处理，包括不限于应用新签冻结、暂停新增授权、应用冻结等。
 |
| 2.封网期内，在未进行紧急发布申请并征得平台允许的前提下，擅自修改应用代码

 |  |
| 3.保障期间，开发者未按平台指定的问题反馈通道进行问题反馈

 |  |
| 4.大促前4小时到大促当天扩容（原有实例ECS、RDS扩容超过1台，新购大于5台），升级带宽除外

 |  |
| 5.大促当天超过10个（含）商家上报平台开发者应用故障

 |  |
| 6.大促当天开发者出现任何应用稳定性的问题，响应时间超过5分钟，处理时间超过30分钟

 |  |
| 7.服务商自助修改地址链路系统响应率小于98%，改地址成功率小于85%

 |  |
| 8. 服务商应用在618期间出现稳定性问题导致影响商家业务。

 |  |

如您对以上内容有任何疑问，请在开放平台支持中心提交[工单](https://work.open.taobao.com/services-submitProblem)或加入服务商保障钉钉群（群号：11730843）咨询，感谢您的理解、支持与配合！

- -淘宝开放平台2024年5月7日